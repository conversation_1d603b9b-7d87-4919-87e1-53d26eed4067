import { User, UserRepository } from "../jobs/PullKinopoiskMarksJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresKinopoiskUserRepository implements UserRepository {
  constructor(private pool: ConnectionPool) {}

  async find(id: string): Promise<User | null> {
    return this.pool.transaction(async (connection) => {
      const user = (
        await connection.query<{ id: number; name: string }>(
          sql`SELECT id, name FROM kinopoisk_user WHERE id = ${id}`,
        )
      )[0];

      if (!user) {
        return null;
      }

      const friends = await connection.query<{
        id: number;
        name: string;
      }>(
        sql`
          SELECT kinopoisk_user.name as name,
                 kinopoisk_friend.friend_id as id
          FROM kinopoisk_friend
          JOIN kinopoisk_user ON kinopoisk_user.id = kinopoisk_friend.friend_id
          WHERE kinopoisk_friend.user_id = ${id}
          ORDER BY kinopoisk_friend.order ASC, kinopoisk_user.name ASC
        `,
      );

      const marks = await connection.query<{
        movie_id: number;
        mark: number | null;
        timestamp: Date;
      }>(
        sql`
          SELECT
            movie_id,
            mark,
            updated_at as timestamp
          FROM
            kinopoisk_movie_mark
          WHERE
            kinopoisk_movie_mark.user_id = ${id}
          ORDER BY
            kinopoisk_movie_mark.order DESC,
            kinopoisk_movie_mark.created_at DESC,
            kinopoisk_movie_mark.movie_id ASC
        `,
      );

      return {
        id: String(user.id),
        friends: friends.map((friend) => ({
          id: String(friend.id),
          name: friend.name,
        })),
        marks: marks.map((mark) => ({
          movie: {
            id: String(mark.movie_id),
          },
          timestamp: mark.timestamp,
          score: mark.mark,
        })),
        name: user.name,
      };
    });
  }

  async set(user: User): Promise<void> {
    return this.pool.transaction(async (connection) => {
      await connection.query(
        sql`CREATE TEMP TABLE tmp_kinopoisk_user (LIKE kinopoisk_user)`,
      );
      await connection.query(
        sql`CREATE TEMP TABLE tmp_kinopoisk_movie_mark (LIKE kinopoisk_movie_mark)`,
      );
      await connection.query(
        sql`CREATE TEMP TABLE tmp_kinopoisk_friend (LIKE kinopoisk_friend)`,
      );
      await connection.copyFrom(
        "COPY tmp_kinopoisk_user (id, name, created_at, updated_at) FROM STDIN",
        [user, ...user.friends.filter((friend) => friend.id !== user.id)].map(
          (u) => ({
            id: Number(u.id),
            name: u.name,
            created_at: new Date(),
            updated_at: new Date(),
          }),
        ),
      );
      await connection.copyFrom(
        `COPY tmp_kinopoisk_movie_mark (user_id, movie_id, mark, "order", created_at, updated_at) FROM STDIN`,
        user.marks.map((mark, index, array) => ({
          user_id: Number(user.id),
          movie_id: Number(mark.movie.id),
          mark: mark.score,
          order: array.length - 1 - index,
          created_at: mark.timestamp,
          updated_at: mark.timestamp,
        })),
      );
      await connection.copyFrom(
        `COPY tmp_kinopoisk_friend (user_id, friend_id, "order", created_at, updated_at) FROM STDIN`,
        user.friends.map((friend, index) => ({
          user_id: Number(user.id),
          friend_id: Number(friend.id),
          order: index,
          created_at: new Date(),
          updated_at: new Date(),
        })),
      );
      await connection.query(
        sql`
          INSERT INTO kinopoisk_user
          (SELECT * FROM tmp_kinopoisk_user)
          ON CONFLICT (id)
            DO UPDATE SET name = excluded.name,
                          updated_at = excluded.updated_at
            WHERE (kinopoisk_user.name) IS DISTINCT FROM (excluded.name)
        `,
      );
      await connection.query(
        sql`
          INSERT INTO kinopoisk_friend
          (SELECT * FROM tmp_kinopoisk_friend)
          ON CONFLICT (user_id, friend_id)
            DO UPDATE SET "order" = excluded.order,
                          updated_at = excluded.updated_at
            WHERE (kinopoisk_friend.order) IS DISTINCT FROM (excluded.order)
        `,
      );
      await connection.query(
        sql`
          DELETE FROM
            kinopoisk_friend
          WHERE
            kinopoisk_friend.user_id = ${user.id}
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_kinopoisk_friend
              WHERE
                tmp_kinopoisk_friend.user_id = kinopoisk_friend.user_id
                AND tmp_kinopoisk_friend.friend_id = kinopoisk_friend.friend_id
            )
        `,
      );
      await connection.query(
        sql`
          INSERT INTO kinopoisk_movie_mark
          (SELECT * FROM tmp_kinopoisk_movie_mark)
          ON CONFLICT (user_id, movie_id)
            DO UPDATE SET
              "order" = excluded.order,
              mark = excluded.mark,
              updated_at = excluded.updated_at
            WHERE (
              kinopoisk_movie_mark.mark,
              kinopoisk_movie_mark.order,
              kinopoisk_movie_mark.updated_at
            ) IS DISTINCT FROM (
              excluded.mark,
              excluded.order,
              excluded.updated_at
            )
        `,
      );
      await connection.query(sql`ANALYZE kinopoisk_movie_mark`);
      await connection.query(
        sql`
          DELETE FROM
            kinopoisk_movie_mark
          WHERE
            kinopoisk_movie_mark.user_id = ${user.id}
            AND NOT EXISTS (
              SELECT
              FROM
                tmp_kinopoisk_movie_mark
              WHERE
                tmp_kinopoisk_movie_mark.user_id = kinopoisk_movie_mark.user_id
                AND tmp_kinopoisk_movie_mark.movie_id = kinopoisk_movie_mark.movie_id
            )
        `,
      );

      await connection.query(sql`DROP TABLE tmp_kinopoisk_user`);
      await connection.query(sql`DROP TABLE tmp_kinopoisk_movie_mark`);
      await connection.query(sql`DROP TABLE tmp_kinopoisk_friend`);
    });
  }
}
