/* eslint-disable no-underscore-dangle */
import assert from "node:assert";
import { GraphQLObjectTypeExtensions } from "graphql";

import { canTrackNewViewNow } from "../../domain/Mark.js";
import * as graphql from "../../web/graphql/types.generated.js";
import PostgresGraphQlResolverContext from "../PostgresGraphQlResolverContext.js";
import sql from "../sql.js";
import { batchResolveNested, FieldSet } from "./_common.js";

const Movie: graphql.MovieResolvers<PostgresGraphQlResolverContext> & {
  __extensions?: GraphQLObjectTypeExtensions;
} = {
  __extensions: {
    preloadBatch: async (
      parents: graphql.Movie[],
      context: PostgresGraphQlResolverContext,
      fieldSet: FieldSet,
    ): Promise<graphql.Movie[]> => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<
          graphql.ResolversParentTypes["Movie"]
        >(sql`
            SELECT
              ${sql.raw(
                Object.keys({ id: {}, ...fieldSet })
                  .filter((field) =>
                    [
                      "id",
                      "kinopoiskId",
                      "slug",
                      "title",
                      "subtitle",
                      "logline",
                      "year",
                      "links",
                      "duration",
                      "directors",
                      "images",
                      "topPositionAllTime",
                      "bestMarksPercentage",
                      "goodMarksPercentage",
                      "viewed",
                      "currentMark",
                    ].includes(field),
                  )
                  .map((field) => `"${field}"`)
                  .join(", "),
              )}
            FROM (
              SELECT
                graphql_movie.id::text as "id",
                graphql_movie.id::text as "kinopoiskId",
                graphql_movie.slug as "slug",
                graphql_movie.title as "title",
                graphql_movie.subtitle as "subtitle",
                graphql_movie.logline as "logline",
                graphql_movie.year as "year",
                graphql_movie.links as "links",
                graphql_movie.duration as "duration",
                COALESCE((
                  SELECT
                    JSON_AGG("v".*)
                  FROM (
                    SELECT
                      graphql_movie_director.person_id::text as "id"
                    FROM
                      graphql_movie_director
                    WHERE
                      graphql_movie_director.movie_id = graphql_movie.id
                    ORDER BY
                      graphql_movie_director.order ASC
                  ) "v"
                ), '[]'::json) as "directors",
                COALESCE((
                  SELECT
                    JSON_AGG("v".*)
                  FROM (
                    SELECT
                      JSON_AGG("t".*) as "sizes"
                    FROM (
                      SELECT
                        graphql_movie_image.url as "url",
                        graphql_movie_image.width as "width",
                        graphql_movie_image.height as "height",
                        graphql_movie_image.order as "order"
                      FROM
                        graphql_movie_image
                      WHERE
                        graphql_movie_image.movie_id = graphql_movie.id
                      ORDER BY
                        graphql_movie_image."order"
                    ) "t"
                    GROUP BY
                      "order"
                  ) "v"
                ), '[]'::json) as "images",
                CASE
                  WHEN graphql_movie_top."position" <= 1000
                  THEN graphql_movie_top."position"
                END as "topPositionAllTime",
                CASE
                  WHEN graphql_movie_top.best_marks_percentage >= 5
                  THEN graphql_movie_top.best_marks_percentage
                END as "bestMarksPercentage",
                graphql_movie_top.good_marks_percentage as "goodMarksPercentage",
                (
                  SELECT
                    EXISTS (
                      SELECT
                      FROM
                        graphql_user_movie_view
                      WHERE
                        graphql_user_movie_view.movie_id = graphql_movie.id
                        AND graphql_user_movie_view.user_id = ${Number(
                          context.accountId,
                        )}
                    )
                ) as "viewed",
                (
                  SELECT
                    json_build_object(
                      'mark', graphql_user_movie_mark.mark,
                      'timestamp', graphql_user_movie_mark.timestamp
                    )
                  FROM
                    graphql_user_movie_mark
                  WHERE
                    graphql_user_movie_mark.movie_id = graphql_movie.id
                    AND graphql_user_movie_mark.user_id = ${Number(
                      context.accountId,
                    )}
                ) as "currentMark"
              FROM
                graphql_movie
              LEFT JOIN
                graphql_movie_top
                ON graphql_movie_top.movie_id = graphql_movie.id
                AND graphql_movie_top.user_id = ${Number(context.accountId)}
              WHERE
                graphql_movie.id IN (${sql.raw(
                  parents.map((parent) => parent.id).join(", "),
                )})
            )
        `);

        const rowById = new Map(rows.map((row) => [row.id, row]));

        return parents.map((parent) => {
          const row = rowById.get(parent.id);

          assert(row, `Cannot find Movie with id "${parent.id}"`);

          return row;
        });
      });
    },
  },

  canTrackViewAgain: {
    async resolve(parent, args, context, resolveInfo) {
      assert(typeof Movie.history !== "function");

      const history = (await Movie.history!.resolve!(
        parent,
        {},
        context,
        resolveInfo,
      ))! as graphql.HistoryItem[];

      return canTrackNewViewNow(
        history.map((x) =>
          x.__typename === "MovieMark"
            ? {
                movieId: x.movieId,
                mark: x.mark,
                timestamp: new Date(x.timestamp),
                type: "mark",
              }
            : {
                movieId: x.movieId,
                timestamp: new Date(x.timestamp),
                type: "view",
              },
        ),
        new Date(),
      );
    },
  },

  genres: {
    resolve: batchResolveNested(async (parent, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<graphql.Genre>(sql`
          SELECT
            graphql_movie_genre.genre_id::text as "id"
          FROM
            graphql_movie_genre
          WHERE
            graphql_movie_genre.movie_id = ${Number(parent.id)}
          ORDER BY
            graphql_movie_genre.genre_id ASC
        `);

        return rows;
      });
    }),
  },

  friendMovieMarksCount: {
    resolve: batchResolveNested(async (parent, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<{
          friendMovieMarksCount: number[];
        }>(sql`
          SELECT
            ARRAY[
              graphql_user_movie_friend_mark."10" + graphql_user_movie_friend_mark."9",
              graphql_user_movie_friend_mark."8" + graphql_user_movie_friend_mark."7",
              graphql_user_movie_friend_mark."6" + graphql_user_movie_friend_mark."5",
              graphql_user_movie_friend_mark."4" + graphql_user_movie_friend_mark."3" + graphql_user_movie_friend_mark."2" + graphql_user_movie_friend_mark."1"
            ] as "friendMovieMarksCount"
          FROM
            graphql_user_movie_friend_mark
          WHERE
            graphql_user_movie_friend_mark.movie_id = ${parent.id}
            AND graphql_user_movie_friend_mark.user_id = ${Number(
              context.accountId,
            )}
        `);

        return rows[0]?.friendMovieMarksCount ?? [0, 0, 0, 0];
      });
    }),
  },

  history: {
    resolve: batchResolveNested(async (parent, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<graphql.HistoryItem>(sql`
          SELECT
            CASE
              WHEN graphql_user_movie_log.mark IS NOT NULL
              THEN 'MovieMark'

              ELSE 'MovieView'
            END as "__typename",
            graphql_user_movie_log.mark as "mark",
            graphql_user_movie_log."timestamp" as "timestamp"
          FROM
            graphql_user_movie_log
          WHERE
            graphql_user_movie_log.movie_id = ${parent.id}
            AND graphql_user_movie_log.user_id = ${Number(context.accountId)}
          ORDER BY
            graphql_user_movie_log."timestamp" DESC,
            graphql_user_movie_log.mark DESC NULLS LAST
        `);

        return rows;
      });
    }),
  },

  links: {
    async resolve(parent) {
      return {
        __typename: "MovieLinks",
        kinopoisk: `https://www.kinopoisk.ru/film/${parent.links.kinopoisk}/`,
        wikipediaEn: parent.links.wikipediaEn
          ? `https://en.wikipedia.org/wiki/${encodeURIComponent(
              parent.links.wikipediaEn,
            )}`
          : undefined,
        wikipediaRu: parent.links.wikipediaRu
          ? `https://ru.wikipedia.org/wiki/${encodeURIComponent(
              parent.links.wikipediaRu,
            )}`
          : undefined,
      };
    },
  },

  watchlistItem: {
    resolve: batchResolveNested(async (parent, _args, context) => {
      return context.pool.transaction(async (connection) => {
        const rows = await connection.query<graphql.WatchlistItem>(sql`
          SELECT
            graphql_watchlist.timestamp as "timestamp"
          FROM
            graphql_watchlist
          WHERE
            graphql_watchlist.movie_id = ${parent.id}
            AND graphql_watchlist.user_id = ${Number(context.accountId)}
        `);

        return rows[0];
      });
    }),
  },
};

export default Movie;
