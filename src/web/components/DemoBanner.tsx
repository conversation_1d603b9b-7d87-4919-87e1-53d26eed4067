import * as React from "react";

import { useAuthentication } from "./Authentication.js";

type DemoBannerState =
  | { step: "initial" }
  | { step: "kinopoisk"; kinopoiskUrl: string }
  | { step: "name"; kinopoiskUrl: string; name: string }
  | { step: "email"; kinopoiskUrl: string; name: string; email: string }
  | { step: "final" };

const DemoBanner: React.FC = () => {
  const [state, setState] = React.useState<DemoBannerState>({
    step: "initial",
  });
  const authentication = useAuthentication();

  if (authentication.state !== "unauthenticated") {
    throw new TypeError("DemoBanner can only be rendered when not signed-in");
  }

  if (state.step === "initial") {
    return (
      <aside className="h-[101px] w-[442px] bg-indigo-600 px-3 pt-[6px] pb-3 text-white">
        <p className="mb-[11px]">
          <span className="font-bold">Демо-режим.</span> Тыкайте куда угодно,
          ваши шалости потом сотрутся.
        </p>
        <div className="flex w-full">
          <button
            className="mr-2 rounded-md px-3 pt-1 pb-1.5 ring-1 ring-indigo-200 ring-inset hover:ring-white"
            type="button"
            onClick={authentication.authenticate}
          >
            Вход
          </button>
          <button
            className="rounded-md px-3 pt-1 pb-1.5 ring-1 ring-indigo-200 ring-inset hover:ring-white"
            onClick={() => setState({ step: "kinopoisk", kinopoiskUrl: "" })}
            type="button"
          >
            Регистрация
          </button>
          {/* <a
            href="/"
            target="_blank"
            className="ml-auto pt-1 underline underline-offset-2"
            rel="noreferrer"
          >
            Что такое Зырь?
          </a> */}
        </div>
      </aside>
    );
  }

  if (state.step === "kinopoisk") {
    return (
      <aside className="h-[101px] w-[442px] bg-indigo-600 px-3 pt-[6px] pb-3 text-white">
        <form>
          <label htmlFor="kinopoisk" className="mb-[11px]">
            Ссылка на профиль КиноПоиска:
            <input
              className="mt-1 block w-[321px] rounded-sm bg-white px-3 pt-1 pb-1.5 text-black placeholder:text-zinc-400"
              id="kinopoisk"
              type="url"
              placeholder="https://www.kinopoisk.ru/user/789114/"
              value={state.kinopoiskUrl}
              onChange={(event) =>
                setState({
                  step: "kinopoisk",
                  kinopoiskUrl: event.target.value,
                })
              }
            />
          </label>
          <button
            className="absolute right-3 bottom-3 rounded-md px-3 pt-1 pb-1.5 ring-1 ring-indigo-200 ring-inset hover:ring-white disabled:text-indigo-200 disabled:ring-indigo-400"
            disabled={
              !/https:\/\/www.kinopoisk.ru\/user\/(\d+)\//.test(
                state.kinopoiskUrl,
              )
            }
            type="submit"
            onClick={() =>
              setState({
                step: "name",
                kinopoiskUrl: state.kinopoiskUrl,
                name: "",
              })
            }
          >
            Далее
          </button>
        </form>
      </aside>
    );
  }

  if (state.step === "name") {
    return (
      <aside className="h-[101px] w-[442px] bg-indigo-600 px-3 pt-[6px] pb-3 text-white">
        <label htmlFor="name" className="mb-[11px]">
          Имя:
          <input
            className="mt-1 block w-[282px] rounded-sm bg-white px-3 pt-1 pb-1.5 text-black placeholder:text-zinc-400"
            id="name"
            type="text"
            placeholder="Константин Констанинопольский"
            value={state.name}
            onChange={(event) =>
              setState({
                step: "name",
                kinopoiskUrl: state.kinopoiskUrl,
                name: event.target.value,
              })
            }
          />
        </label>
        <button
          className="absolute right-3 bottom-3 rounded-md px-3 pt-1 pb-1.5 ring-1 ring-indigo-200 ring-inset hover:ring-white disabled:text-indigo-200 disabled:ring-indigo-400"
          type="submit"
          onClick={() =>
            setState({
              step: "email",
              kinopoiskUrl: state.kinopoiskUrl,
              name: state.name,
              email: "",
            })
          }
        >
          Далее
        </button>
      </aside>
    );
  }

  if (state.step === "email") {
    return (
      <aside className="h-[101px] w-[442px] bg-indigo-600 px-3 pt-[6px] pb-3 text-white">
        <label htmlFor="email" className="mb-[11px]">
          Имейл, на который выслать инвайт:
          <input
            className="mt-1 block w-[231px] rounded-sm bg-white px-3 pt-1 pb-1.5 text-black placeholder:text-zinc-400"
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={state.email}
            onChange={(event) =>
              setState({
                step: "email",
                kinopoiskUrl: state.kinopoiskUrl,
                name: state.name,
                email: event.target.value,
              })
            }
          />
        </label>
        <button
          className="absolute right-3 bottom-3 rounded-md px-3 pt-1 pb-1.5 ring-1 ring-indigo-200 ring-inset hover:ring-white disabled:text-indigo-200 disabled:ring-indigo-400"
          type="submit"
          disabled={!state.email.includes("@")}
          onClick={() => {
            authentication.invite({
              email: state.email,
              kinopoiskUrl: state.kinopoiskUrl,
              name: state.name,
            });
            setState({
              step: "final",
            });
          }}
        >
          Запросить инвайт
        </button>
      </aside>
    );
  }

  return (
    <aside className="h-[101px] w-[442px] bg-indigo-600 px-3 pt-[6px] pb-3 text-white">
      <p>
        Ожидайте! Обычно инвайт приходит в течение суток. Задержка вызвана тем,
        что нужно успеть выкачать ваши данные из КиноПоиска.
      </p>
    </aside>
  );
};

export default DemoBanner;
