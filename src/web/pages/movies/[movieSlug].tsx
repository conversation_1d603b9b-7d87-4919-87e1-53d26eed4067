import classNames from "classnames";
import { differenceInCalendarDays, differenceInMonths } from "date-fns";
import * as React from "react";

import { useAuthentication } from "../../components/Authentication.js";
import Button from "../../components/Button.js";
import Head from "../../components/Head.js";
import { pluralize } from "../../components/I18n.js";
import Join from "../../components/Join.js";
import { Rewrite, useRouter } from "../../components/Router.js";
import { graphql, useMutation, useQuery } from "../../graphql/client.js";

function formatDate(date: Date): string {
  const months = [
    "янв",
    "фев",
    "мар",
    "апр",
    "мая",
    "июн",
    "июл",
    "авг",
    "сен",
    "окт",
    "ноя",
    "дек",
  ];

  return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
}

function formatWatchlistedRelativeDate(date: Date): string {
  const now = new Date();
  const daysPassed = differenceInCalendarDays(now, date);

  if (daysPassed === 0) {
    return "ожидает с сегодня";
  }

  if (daysPassed === 1) {
    return "ожидает со вчера";
  }

  if (daysPassed < 60) {
    return pluralize(daysPassed, {
      one: `ожидает ${daysPassed} день`,
      few: `ожидает ${daysPassed} дня`,
      many: `ожидает ${daysPassed} дней`,
      other: `ожидает ${daysPassed} дней`,
    });
  }

  const monthsPassed = differenceInMonths(now, date);

  if (monthsPassed < 12) {
    return pluralize(monthsPassed, {
      one: `ожидает ${monthsPassed} месяц`,
      few: `ожидает ${monthsPassed} месяца`,
      many: `ожидает ${monthsPassed} месяцев`,
      other: `ожидает ${monthsPassed} месяцев`,
    });
  }

  return "ожидает больше года";
}

const MoviePageGetMovie = graphql(/* GraphQL */ `
  query MoviePageGetMovie($kinopoiskId: ID, $slug: String) {
    movie(kinopoiskId: $kinopoiskId, slug: $slug) {
      id
      kinopoiskId
      slug
      title
      subtitle
      logline
      goodMarksPercentage
      bestMarksPercentage
      topPositionAllTime
      friendMovieMarksCount
      year
      duration {
        minutes
      }
      directors {
        id
        fullName
        slug
        movies(sort: CHRONOLOGICAL) {
          id
          kinopoiskId
          slug
          title
          goodMarksPercentage
          bestMarksPercentage
          topPositionAllTime
          topPositionAllTime
          viewed
          currentMark {
            mark
          }
          images {
            sizes {
              height
              url
              width
            }
          }
          year
        }
      }
      genres {
        id
        label
        slug
      }
      images {
        sizes {
          height
          url
          width
        }
      }
      links {
        kinopoisk
        wikipediaEn
        wikipediaRu
      }
      currentMark {
        mark
      }
      viewed
      canTrackViewAgain
      history {
        ... on MovieView {
          __typename
          timestamp
        }

        ... on MovieMark {
          __typename
          timestamp
          mark
        }
      }
      watchlistItem {
        timestamp
      }
    }
  }
`);

const MoviePageAddToWatchlist = graphql(/* GraphQL */ `
  mutation MoviePageAddToWatchlist($movieId: ID!) {
    addToWatchlist(movieId: $movieId)
  }
`);

const MoviePageRemoveFromWatchlist = graphql(/* GraphQL */ `
  mutation MoviePageRemoveFromWatchlist($movieId: ID!) {
    removeFromWatchlist(movieId: $movieId)
  }
`);

const TrackMovieView = graphql(/* GraphQL */ `
  mutation TrackMovieView($movieId: ID!) {
    trackMovieView(movieId: $movieId)
  }
`);

const MoviePage: React.FC = () => {
  const router = useRouter();
  const authentication = useAuthentication();
  const timelineScrollContainerRef = React.useRef<HTMLUListElement | null>(
    null,
  );
  const timelineSelectedTileRef = React.useRef<HTMLLIElement | null>(null);

  if (
    router.pattern !== "/movies/:movieSlug" &&
    router.pattern !== "/movies/by-kinopoisk-id/:kinopoiskId" &&
    router.pattern !== "/people/:personSlug/movies/:movieSlug" &&
    router.pattern !== "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId"
  ) {
    throw new Error("Unexpected pattern");
  }

  const { data, refetch } = useQuery(
    MoviePageGetMovie,
    {
      kinopoiskId:
        router.pattern === "/movies/by-kinopoisk-id/:kinopoiskId" ||
        router.pattern ===
          "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId"
          ? router.params.kinopoiskId
          : null,
      slug:
        router.pattern === "/movies/:movieSlug" ||
        router.pattern === "/people/:personSlug/movies/:movieSlug"
          ? router.params.movieSlug
          : null,
    },
    {
      keepPreviousData: true,
    },
  );
  const { mutate: trackMovieView, isLoading: isTrackingMovieView } =
    useMutation(TrackMovieView, {
      onSuccess() {
        return refetch();
      },
    });
  const { mutate: addToWatchlist, isLoading: isAddingToWatchlist } =
    useMutation(MoviePageAddToWatchlist, {
      onSuccess() {
        return refetch();
      },
    });
  const { mutate: removeFromWatchlist, isLoading: isRemovingFromWatchlist } =
    useMutation(MoviePageRemoveFromWatchlist, {
      onSuccess() {
        return refetch();
      },
    });
  const hasData = data !== undefined;

  React.useEffect(() => {
    if (
      hasData &&
      timelineScrollContainerRef.current &&
      timelineSelectedTileRef.current
    ) {
      timelineScrollContainerRef.current.scrollTo(
        timelineSelectedTileRef.current.offsetLeft - 24,
        0,
      );
    }
  }, [hasData]);

  const movie = data?.movie;

  if (!data) {
    return null;
  }

  if (!movie) {
    return <Rewrite to={{ pathname: "/404" }} />;
  }

  let timelinePerson = movie.directors[0];

  if ("personSlug" in router.params && router.params.personSlug) {
    const personSlug = router.params.personSlug;
    const urlPerson = movie.directors.find(
      (director) => director.slug === personSlug,
    );

    if (!urlPerson) {
      return <Rewrite to={{ pathname: "/404" }} />;
    }

    timelinePerson = urlPerson;
  }

  const totalFriendsCount = movie.friendMovieMarksCount.reduce(
    (a, b) => a + b,
    0,
  );

  return (
    <>
      <Head>
        <title>{movie.title} – Зырь</title>
        <link
          rel="canonical"
          href={new URL(
            movie.slug
              ? router.stringify("/movies/:movieSlug", {
                  movieSlug: movie.slug,
                })
              : router.stringify("/movies/by-kinopoisk-id/:kinopoiskId", {
                  kinopoiskId: movie.kinopoiskId,
                }),
            router.url.origin,
          ).toString()}
        />
      </Head>

      <main className="container mx-auto px-5 antialiased">
        {movie.subtitle ? (
          <h2 className="-mb-2 translate-y-2">{movie.subtitle}</h2>
        ) : null}
        <h1 className="mt-2 -translate-x-1 text-7xl font-bold tracking-tight">
          {movie.title}
        </h1>

        <div className="mt-7 flex">
          <div className="w-[180px] shrink-0 pr-5">
            <div className="mt-[-6px]" />

            {movie.logline ? (
              <p className="text-xs break-words hyphens-auto">
                {movie.logline}
              </p>
            ) : null}

            {movie.directors.length > 0 ? (
              <>
                <h2
                  className={classNames("text-xs", { "mt-3": movie.logline })}
                >
                  {movie.directors.length > 1 ? "Режиссёры" : "Режиссёр"}
                </h2>
                <span>
                  <ul>
                    {movie.directors.map((director) => (
                      <li key={director.id}>
                        <a
                          className="text-indigo-700 transition-colors hover:text-indigo-400 hover:transition-none"
                          href={router.stringify("/people/:personSlug", {
                            personSlug: director.slug,
                          })}
                        >
                          {director.fullName}
                        </a>
                      </li>
                    ))}
                  </ul>
                </span>
              </>
            ) : null}

            <div
              className={classNames("flex space-x-3", {
                "mt-3": movie.directors.length > 0,
              })}
            >
              {movie.duration ? (
                <span className="w-1/2 flex-grow">
                  <h2 className="text-xs">Время</h2>
                  <span>
                    {movie.duration.minutes > 60
                      ? `${Math.floor(movie.duration.minutes / 60)}ч `
                      : ""}
                    {movie.duration.minutes % 60}м
                  </span>
                </span>
              ) : null}

              {movie.year ? (
                <span className="w-1/2 flex-grow">
                  <h2 className="text-xs">Год</h2>
                  <span>{movie.year}</span>
                </span>
              ) : null}
            </div>

            {movie.genres.length > 0 ? (
              <>
                <h2 className="mt-3 text-xs">
                  {movie.genres.length > 1 ? "Жанры" : "Жанр"}
                </h2>
                <Join delimiter=", ">
                  {movie.genres.map((genre) => (
                    <a
                      key={genre.id}
                      className="text-indigo-700 transition-colors hover:text-indigo-400 hover:transition-none"
                      href={router.stringify("/movies/genre/:genre", {
                        genre: genre.slug,
                      })}
                    >
                      {genre.label}
                    </a>
                  ))}
                </Join>
              </>
            ) : null}

            <h2 className="mt-3 text-xs">Ссылки</h2>
            <a
              className="block text-indigo-700 transition-colors hover:text-indigo-400 hover:transition-none"
              href={movie.links.kinopoisk}
              target="_blank"
              rel="noreferrer"
            >
              КиноПоиск
            </a>
            {movie.links.wikipediaRu ? (
              <a
                className="block text-indigo-700 transition-colors hover:text-indigo-400 hover:transition-none"
                href={movie.links.wikipediaRu}
                target="_blank"
                rel="noreferrer"
              >
                Википедия
              </a>
            ) : null}
            {movie.links.wikipediaEn ? (
              <a
                className="block text-indigo-700 transition-colors hover:text-indigo-400 hover:transition-none"
                href={movie.links.wikipediaEn}
                target="_blank"
                rel="noreferrer"
              >
                Wikipedia
              </a>
            ) : null}

            {totalFriendsCount > 0 ? (
              <>
                <h2 className="mt-3 text-xs">Оценки друзей</h2>

                <div className="flex space-x-1.5 leading-none">
                  {movie.topPositionAllTime ? (
                    <div>№&nbsp;{movie.topPositionAllTime}</div>
                  ) : null}

                  {movie.bestMarksPercentage ? (
                    <div className="inline-flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="mr-0.5 inline h-[0.8lh] w-[0.8lh] align-middle text-pink-600"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z" />
                      </svg>
                      {movie.bestMarksPercentage}%
                    </div>
                  ) : null}

                  {movie.goodMarksPercentage ? (
                    <div className="inline-flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className={classNames(
                          "mr-0.5 inline h-[0.8lh] w-[0.8lh]",
                          {
                            "text-indigo-500": movie.goodMarksPercentage >= 65,
                            "text-zinc-500": movie.goodMarksPercentage < 65,
                          },
                        )}
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M13 3a3 3 0 0 1 2.995 2.824l.005 .176v4h2a3 3 0 0 1 2.98 2.65l.015 .174l.005 .176l-.02 .196l-1.006 5.032c-.381 1.626 -1.502 2.796 -2.81 2.78l-.164 -.008h-8a1 1 0 0 1 -.993 -.883l-.007 -.117l.001 -9.536a1 1 0 0 1 .5 -.865a2.998 2.998 0 0 0 1.492 -2.397l.007 -.202v-1a3 3 0 0 1 3 -3z" />
                        <path d="M5 10a1 1 0 0 1 .993 .883l.007 .117v9a1 1 0 0 1 -.883 .993l-.117 .007h-1a2 2 0 0 1 -1.995 -1.85l-.005 -.15v-7a2 2 0 0 1 1.85 -1.995l.15 -.005h1z" />
                      </svg>
                      {movie.goodMarksPercentage}%
                    </div>
                  ) : null}
                </div>

                {totalFriendsCount <= 53 ? (
                  <div
                    className={classNames("mt-1 grid gap-px", {
                      "h-[4px]":
                        totalFriendsCount < Math.floor((160 + 1) / (1 + 4)),
                      "h-[3px]":
                        totalFriendsCount >= Math.floor((160 + 1) / (1 + 4)),
                    })}
                    style={{
                      gridTemplateColumns:
                        totalFriendsCount > Math.floor((160 + 1) / (1 + 3))
                          ? `repeat(${Math.floor((160 + 1) / (1 + 2))}, 2px)`
                          : totalFriendsCount > Math.floor((160 + 1) / (1 + 4))
                          ? `repeat(${Math.floor((160 + 1) / (1 + 3))}, 3px)`
                          : `repeat(${Math.floor((160 + 1) / (1 + 4))}, 4px)`,
                    }}
                  >
                    {times(movie.friendMovieMarksCount[0], (i) => (
                      <div key={i} className="bg-pink-500" />
                    ))}
                    {times(movie.friendMovieMarksCount[1], (i) => (
                      <div key={i} className="bg-indigo-500" />
                    ))}
                    {times(
                      movie.friendMovieMarksCount[2] +
                        movie.friendMovieMarksCount[3],
                      (i) => (
                        <div key={i} className="bg-zinc-300" />
                      ),
                    )}
                  </div>
                ) : (
                  <div className="mt-1 flex h-[3px] w-full flex-wrap">
                    <div
                      className="bg-pink-500"
                      style={{
                        width: `${
                          (100 * movie.friendMovieMarksCount[0]) /
                          totalFriendsCount
                        }%`,
                      }}
                    />
                    <div
                      className="bg-indigo-500"
                      style={{
                        width: `${
                          (100 * movie.friendMovieMarksCount[1]) /
                          totalFriendsCount
                        }%`,
                      }}
                    />
                    <div
                      className="bg-zinc-300"
                      style={{
                        width: `${
                          (100 *
                            (movie.friendMovieMarksCount[2] +
                              movie.friendMovieMarksCount[3])) /
                          totalFriendsCount
                        }%`,
                      }}
                    />
                  </div>
                )}
              </>
            ) : null}

            <div className="mt-5">
              <Button
                onClick={() => {
                  if (movie.watchlistItem) {
                    removeFromWatchlist({ movieId: movie.id });
                  } else {
                    addToWatchlist({ movieId: movie.id });
                  }
                }}
                disabled={
                  authentication.state !== "authenticated" ||
                  isAddingToWatchlist ||
                  isRemovingFromWatchlist
                }
                type="button"
                size="lg"
                variant={movie.watchlistItem ? "outline-succeed" : "primary"}
                fullWidth
              >
                <span
                  className={classNames({
                    "mt-[-6px] block text-center": Boolean(movie.watchlistItem),
                  })}
                >
                  Буду смотреть
                </span>
                {movie.watchlistItem ? (
                  <span
                    title={formatDate(new Date(movie.watchlistItem.timestamp))}
                    className="mt-[-4px] block text-xs text-zinc-400"
                  >
                    {formatWatchlistedRelativeDate(
                      new Date(movie.watchlistItem.timestamp),
                    )}
                  </span>
                ) : null}
              </Button>
            </div>

            <div className="mt-2">
              <Button
                onClick={() => trackMovieView({ movieId: movie.id })}
                disabled={
                  authentication.state !== "authenticated" ||
                  !movie.canTrackViewAgain ||
                  isTrackingMovieView
                }
                type="button"
                size="lg"
                variant="outline"
                fullWidth
              >
                {movie.viewed ? "Посмотрел снова" : "Посмотрел"}
              </Button>
            </div>

            {movie.viewed ? (
              <a
                href={movie.links.kinopoisk}
                target="_blank"
                className="mt-2 block"
              >
                <Button type="button" size="lg" variant="outline" fullWidth>
                  {movie.currentMark ? (
                    <span>
                      <span
                        className={classNames(
                          "mr-2 inline-block h-[1lh] w-[1lh] rounded-full text-center tracking-tighter",
                          {
                            "text-[16px] tracking-tighter":
                              movie.currentMark.mark === 10,
                            "bg-pink-700 text-white":
                              movie.currentMark.mark >= 9 &&
                              movie.currentMark.mark <= 10,
                            "bg-indigo-700 text-white":
                              movie.currentMark.mark >= 7 &&
                              movie.currentMark.mark <= 8,
                            "bg-zinc-300 text-zinc-950":
                              movie.currentMark.mark <= 6,
                          },
                        )}
                      >
                        {movie.currentMark.mark}
                      </span>
                      Моя оценка
                    </span>
                  ) : (
                    "Оценить"
                  )}
                </Button>
              </a>
            ) : null}

            <table className="mt-3 w-full border-t border-b border-zinc-200 text-xs">
              <tbody>
                {movie.history.map((item) => (
                  <tr
                    // eslint-disable-next-line no-underscore-dangle
                    key={item.__typename + item.timestamp}
                    className="border-b border-zinc-200"
                  >
                    <td
                      className="w-1/2 p-0 pt-px pb-0.5"
                      title={new Date(item.timestamp).toLocaleString()}
                    >
                      {formatDate(new Date(item.timestamp))}
                    </td>
                    <td className="w-1/2 p-0 pt-px pb-0.5 pl-1.5">
                      {/* eslint-disable-next-line no-underscore-dangle */}
                      {item.__typename === "MovieMark"
                        ? `поставил ${item.mark}`
                        : "посмотрел"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {movie.images.length > 0 ? (
            <div className="aspect-video">
              <img
                alt={movie.title}
                src={movie.images[0].sizes[0].url}
                width={movie.images[0].sizes[0].width}
                height={movie.images[0].sizes[0].height}
              />
            </div>
          ) : (
            <div className="aspect-video w-full bg-zinc-100" />
          )}
        </div>
      </main>

      {timelinePerson && timelinePerson.movies.length > 0 ? (
        <aside className="mt-6 w-full border-t border-t-zinc-200 antialiased">
          <h3 className="px-5 pt-4">Режиссёр {timelinePerson.fullName}</h3>
          <ul
            className="flex space-x-4 overflow-x-scroll px-5 pt-2 pb-3"
            ref={timelineScrollContainerRef}
          >
            {timelinePerson.movies
              .sort((a, b) => (a.year ?? Infinity) - (b.year ?? Infinity))
              .map((m) => (
                <li
                  key={m.id}
                  className="w-[178px] shrink-0"
                  ref={m.id === movie.id ? timelineSelectedTileRef : undefined}
                >
                  <a
                    href={
                      m.slug
                        ? router.stringify(
                            "/people/:personSlug/movies/:movieSlug",
                            {
                              personSlug: timelinePerson.slug,
                              movieSlug: m.slug,
                            },
                          )
                        : undefined
                    }
                    onClick={(event) => {
                      if (!event.metaKey) {
                        event.preventDefault();
                        router.push(
                          m.slug
                            ? router.stringify(
                                "/people/:personSlug/movies/:movieSlug",
                                {
                                  personSlug: timelinePerson.slug,
                                  movieSlug: m.slug,
                                },
                              )
                            : router.stringify(
                                "/people/:personSlug/movies/by-kinopoisk-id/:kinopoiskId",
                                {
                                  personSlug: timelinePerson.slug,
                                  kinopoiskId: m.kinopoiskId,
                                },
                              ),
                        );
                      }
                    }}
                  >
                    <span className="relative inline-block">
                      {m.images.length > 0 ? (
                        <img
                          className={classNames("h-[100px] w-[178px]", {
                            "ring-4 ring-indigo-600": m.id === movie.id,
                          })}
                          alt={m.title}
                          src={m.images[0].sizes[0].url}
                          width={m.images[0].sizes[0].width}
                          height={m.images[0].sizes[0].height}
                        />
                      ) : (
                        <div
                          className={classNames(
                            "h-[100px] w-[178px] bg-zinc-100",
                            {
                              "ring-4 ring-indigo-600": m.id === movie.id,
                            },
                          )}
                        />
                      )}
                      {m.viewed ? (
                        <div className="absolute top-1.5 right-1.5">
                          <span
                            className={classNames(
                              "block h-6 w-6 rounded-full pt-[2px] text-center text-[16px] leading-[21px] ring-2 ring-white",
                              {
                                "text-[16px] tracking-tighter":
                                  m.currentMark?.mark === 10,
                                "bg-pink-600 text-white":
                                  m.currentMark &&
                                  m.currentMark.mark >= 9 &&
                                  m.currentMark.mark <= 10,
                                "bg-indigo-600 text-white":
                                  m.currentMark &&
                                  m.currentMark.mark >= 7 &&
                                  m.currentMark.mark <= 8,
                                "bg-zinc-300 text-zinc-950":
                                  m.currentMark && m.currentMark.mark <= 6,
                              },
                            )}
                          >
                            {m.currentMark?.mark ?? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 16 16"
                                className="mx-auto mt-[1px] h-[19px] w-[19px]"
                              >
                                <path
                                  d="M8 12.5C3 12.5.3 8.4.2 8.3L0 8l.1-.3C.2 7.6 2.5 3.5 8 3.5s7.8 4.1 7.8 4.3l.2.3-.2.2c-.1.2-2.8 4.2-7.8 4.2zM1.2 8c.7.8 3.1 3.5 6.8 3.5 3.8 0 6.1-2.7 6.8-3.5-.6-.9-2.6-3.5-6.8-3.5-4.2 0-6.2 2.6-6.8 3.5z"
                                  stroke="none"
                                />
                                <path
                                  d="M8 10.5c-1.9 0-3.5-1.6-3.5-3.5S6.1 3.5 8 3.5s3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5zm0-6C6.6 4.5 5.5 5.6 5.5 7S6.6 9.5 8 9.5s2.5-1.1 2.5-2.5S9.4 4.5 8 4.5z"
                                  stroke="none"
                                />
                                <circle cx="6.7" cy="6.5" r="1.5" />
                              </svg>
                            )}
                          </span>
                        </div>
                      ) : null}
                      <span
                        className={classNames(
                          "absolute bottom-[-3px] left-0 space-x-1.5 overflow-hidden rounded-tr-xs pr-1 pl-0.5 text-xs font-bold",
                          {
                            "bg-indigo-600 text-white": m.id === movie.id,
                            "bg-white": m.id !== movie.id,
                          },
                        )}
                      >
                        {m.topPositionAllTime ? (
                          <span>№&nbsp;{m.topPositionAllTime}</span>
                        ) : null}
                        {m.bestMarksPercentage ? (
                          <span
                            className={classNames({
                              "text-pink-600": m.id !== movie.id,
                            })}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                              className="mr-0.5 inline h-[0.7lh] w-[0.7lh] translate-y-[-1px] align-middle"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M6.979 3.074a6 6 0 0 1 4.988 1.425l.037 .033l.034 -.03a6 6 0 0 1 4.733 -1.44l.246 .036a6 6 0 0 1 3.364 10.008l-.18 .185l-.048 .041l-7.45 7.379a1 1 0 0 1 -1.313 .082l-.094 -.082l-7.493 -7.422a6 6 0 0 1 3.176 -10.215z" />
                            </svg>
                            {m.bestMarksPercentage}%
                          </span>
                        ) : null}
                        {m.goodMarksPercentage ? (
                          <span
                            className={classNames({
                              "text-indigo-500":
                                m.id !== movie.id &&
                                m.goodMarksPercentage >= 65,
                              "text-zinc-500":
                                m.id !== movie.id && m.goodMarksPercentage < 65,
                            })}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                              className="mr-0.5 inline h-[0.7lh] w-[0.7lh] translate-y-[-1px]"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M13 3a3 3 0 0 1 2.995 2.824l.005 .176v4h2a3 3 0 0 1 2.98 2.65l.015 .174l.005 .176l-.02 .196l-1.006 5.032c-.381 1.626 -1.502 2.796 -2.81 2.78l-.164 -.008h-8a1 1 0 0 1 -.993 -.883l-.007 -.117l.001 -9.536a1 1 0 0 1 .5 -.865a2.998 2.998 0 0 0 1.492 -2.397l.007 -.202v-1a3 3 0 0 1 3 -3z" />
                              <path d="M5 10a1 1 0 0 1 .993 .883l.007 .117v9a1 1 0 0 1 -.883 .993l-.117 .007h-1a2 2 0 0 1 -1.995 -1.85l-.005 -.15v-7a2 2 0 0 1 1.85 -1.995l.15 -.005h1z" />
                            </svg>
                            {m.goodMarksPercentage}%
                          </span>
                        ) : null}
                      </span>
                    </span>
                    <span
                      className={classNames(
                        "inline-block w-full overflow-hidden text-ellipsis whitespace-nowrap",
                        {
                          "text-indigo-600": m.id === movie.id,
                        },
                      )}
                      title={`${m.title}${m.year ? ` (${m.year})` : ""}`}
                    >
                      {m.title}{" "}
                      <span
                        className={classNames({
                          "text-zinc-400": m.id !== movie.id,
                          "text-indigo-300": m.id === movie.id,
                        })}
                      >
                        {m.year}
                      </span>
                    </span>
                  </a>
                </li>
              ))}
          </ul>
        </aside>
      ) : null}
    </>
  );
};

function times<T>(n: number, callback: (i: number) => T): T[] {
  const result: T[] = [];

  for (let i = 0; i < n; i += 1) {
    result.push(callback(i));
  }

  return result;
}

export default MoviePage;
